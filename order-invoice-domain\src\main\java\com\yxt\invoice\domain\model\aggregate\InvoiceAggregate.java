package com.yxt.invoice.domain.model.aggregate;

import com.yxt.common.ddd.domain.model.BaseAggregateRoot;
import com.yxt.invoice.domain.command.ExistsOrderInvoiceCommand;
import com.yxt.invoice.domain.event.BaseInvoiceDomainEvent;
import com.yxt.invoice.domain.event.create.InvoiceApplyEvent;
import com.yxt.invoice.domain.event.create.InvoiceCheckProviderPullEvent;
import com.yxt.invoice.domain.event.create.RedInvoiceApplyEvent;
import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;

@Data
public class InvoiceAggregate extends BaseAggregateRoot<BaseInvoiceDomainEvent<?>> {


  private InvoiceMain invoiceMain;

  private List<InvoiceDetail> invoiceDetailList;

  public static InvoiceAggregate rebuild(InvoiceMain main, List<InvoiceDetail> details) {
    // 构建逻辑
    InvoiceAggregate aggregate = new InvoiceAggregate();
    aggregate.setInvoiceMain(main);
    aggregate.setInvoiceDetailList(details);
    return aggregate;
  }


  public void existsApply() {
    this.invoiceMain.setInvoiceStatus(InvoiceStatusEnum.FAIL);
    this.invoiceMain.setInvoiceErrMsg("重复申请,已开具发票,请稍后查询");
    this.addDomainEvents(new InvoiceCheckProviderPullEvent(this));
  }

  public void invoiceApply() {
    this.addDomainEvents(new InvoiceApplyEvent(this));
  }

  public boolean isAllowRedInvoice() {
    return this.invoiceMain.getInvoiceStatus().equals(InvoiceStatusEnum.SUCCESS)
        && this.invoiceMain.getInvoiceRedBlueType().equals(InvoiceRedBlueTypeEnum.TAX_INVOICE);
  }

  public boolean isMemberOrder() {
    return this.invoiceMain.isMemberOrder();
  }

  public void redCreditApply(String invoiceMainNo, String operatorUserId, String redCreditReason,
      String notes, Date applyTime) {

    this.invoiceMain.redCreditApply(invoiceMainNo, operatorUserId, redCreditReason, notes,
        applyTime);
    this.invoiceDetailList.forEach(
        invoiceDetail -> invoiceDetail.redCreditApply(this.invoiceMain.getInvoiceMainNo()));
    this.addDomainEvents(new RedInvoiceApplyEvent(this));
  }

  public boolean isRedCreditInvoice() {

    return this.invoiceMain.isRedCreditInvoice();
  }


  public InvoiceAggregate clone() {
    InvoiceAggregate invoiceAggregate = new InvoiceAggregate();
    InvoiceMain cloneInvoiceMain = this.invoiceMain.clone();
    invoiceAggregate.setInvoiceMain(cloneInvoiceMain);
    invoiceAggregate.setInvoiceDetailList(
        this.invoiceDetailList.stream().map(InvoiceDetail::clone).collect(Collectors.toList()));
    return invoiceAggregate;
  }

  public void updateApplyInfo(String invoiceCode, String providerParam) {

    this.invoiceMain.setInvoiceCode(invoiceCode);
    this.invoiceMain.setProviderParam(providerParam);
  }


  public ExistsOrderInvoiceCommand createExistsOrderInvoiceCommand() {
    ExistsOrderInvoiceCommand command = new ExistsOrderInvoiceCommand();
    command.setOrderNo(this.invoiceMain.getOrderNo().getOrderNo());
    command.setTransactionChannel(this.invoiceMain.getTransactionChannel());
    command.setPosNo(this.invoiceMain.getPosNo());
    command.setThirdPlatformCode(this.invoiceMain.getThirdPlatformCode());
    command.setThirdOrderNo(this.invoiceMain.getThirdOrderNo());
    command.setStoreCode(this.invoiceMain.getOrganizationCode());
    return command;

  }


}

