package com.yxt.invoice.sdk.api;

import com.yxt.invoice.sdk.dto.req.SimpleApplyInvoiceMainReqDto;
import com.yxt.invoice.sdk.dto.req.ApplyInvoiceReqDto;
import com.yxt.invoice.sdk.dto.req.ApplyRedCreditReqDto;
import com.yxt.invoice.sdk.dto.req.SimpleApplyRedInvoiceMainReqDto;
import com.yxt.invoice.sdk.dto.res.ApplyInvoiceMainResDto;
import com.yxt.invoice.sdk.dto.res.ApplyInvoiceResDto;
import com.yxt.invoice.sdk.dto.res.ApplyRedCreditResDto;
import com.yxt.lang.dto.api.ResponseBase;

import java.util.List;

/**
 * 发票服务SDK接口
 * 为医药电商平台提供开票相关服务
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public interface InvoiceApi {



    /**
     * 申请开票
     *
     * @param reqDto 开票申请请求
     * @return 开票申请结果
     */
    ResponseBase<List<ApplyInvoiceResDto>> simpleApplyInvoice(SimpleApplyInvoiceMainReqDto reqDto);




    /**
     * 申请开票
     * 
     * @param reqDto 开票申请请求
     * @return 开票申请结果
     */
    ResponseBase<ApplyRedCreditResDto> simpleApplyRedInvoice(SimpleApplyRedInvoiceMainReqDto reqDto);






}
